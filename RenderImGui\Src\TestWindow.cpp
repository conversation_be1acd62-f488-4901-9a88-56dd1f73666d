#include "httplib.h"
#include "json.hpp"

#include "Engine.h"
#include "UnRender.h"

#include "imgui.h"
#include "RenderImGui.h"


#include <vector>
#include <string>
#include <algorithm>
#include <iostream>
#include <functional>

struct Server {
    std::string name;
    int players;
    int maxPlayers;
    float ping;
    bool isLAN;
    std::string location;
    std::string map;  // Added map column
    bool isConnected;

    Server(const std::string& n, int p, int mp, float pi, bool lan, const std::string& loc, const std::string& m)
        : name(n), players(p), maxPlayers(mp), ping(pi), isLAN(lan), location(loc), map(m), isConnected(false) {}
};

// Example server data (replace with actual server query logic later)
std::vector<Server> internetServers = {
    {"Server 1", 12, 20, 35.2f, false, "USA", "de_dust2"},
    {"Server 2", 8, 20, 56.8f, false, "Germany", "cs_office"},
    {"Server 3", 18, 20, 23.4f, false, "UK", "de_inferno"}
};

std::vector<Server> lanServers = {
    {"LAN Server A", 4, 10, 10.1f, true, "Local Network", "de_nuke"},
    {"LAN Server B", 5, 10, 12.5f, true, "Local Network", "cs_italy"},
    {"LAN Server C", 3, 10, 14.2f, true, "Local Network", "de_mirage"}
};

class RENDERIMGUI_API URenderImGuiTestWindow : public URenderImGuiWindow
{
    DECLARE_CLASS(URenderImGuiTestWindow,URenderImGuiWindow,CLASS_Transient,RenderImGui)

    void Paint(FSceneNode* Frame);

    NO_DEFAULT_CONSTRUCTOR(URenderImGuiTestWindow);
};
IMPLEMENT_CLASS(URenderImGuiTestWindow);

void ConnectToServer(FSceneNode* GFrame, const Server& server) {
    GLog->Logf(TEXT("Connecting to: %s located in %s"), appFromAnsi(server.name.c_str()), appFromAnsi(server.location.c_str()));
    // Simulate connection logic (e.g., network code to actually connect)
    GFrame->Viewport->Exec(*FString::Printf(TEXT("start %s"), appFromAnsi(server.map.c_str())));
}

// Function to draw the server list in a table with sorting functionality
void DrawServerListTable(std::vector<Server>& servers, std::function<void(FSceneNode* GFrame, const Server&)> onConnect, int& selectedServerIndex, FSceneNode* GFrame) {
    if (ImGui::BeginTable("Server List", 6, ImGuiTableFlags_Sortable | ImGuiTableFlags_Resizable | ImGuiTableFlags_Hideable | ImGuiTableFlags_SizingFixedFit | ImGuiTableFlags_NoSavedSettings)) {
        ImGui::TableSetupColumn("Name", ImGuiTableColumnFlags_WidthStretch | ImGuiTableColumnFlags_DefaultSort);
        ImGui::TableSetupColumn("Players", ImGuiTableColumnFlags_WidthStretch);
        ImGui::TableSetupColumn("Ping", ImGuiTableColumnFlags_WidthStretch);
        ImGui::TableSetupColumn("Location", ImGuiTableColumnFlags_WidthStretch);
        ImGui::TableSetupColumn("Map", ImGuiTableColumnFlags_WidthStretch);
        ImGui::TableSetupColumn("Connect", ImGuiTableColumnFlags_WidthFixed | ImGuiTableColumnFlags_NoSort | ImGuiTableColumnFlags_NoHeaderLabel, 100.0f); // Mark "Connect" column as non-sortable

        ImGui::TableHeadersRow();

        // Check sorting order
        const ImGuiTableSortSpecs* sortSpecs = ImGui::TableGetSortSpecs();
        if (sortSpecs->SpecsCount > 0) {
            const ImGuiTableColumnSortSpecs& specs = sortSpecs->Specs[0];  // Only the first column is used for sorting here

            // Sort servers based on the selected column and sort direction
            if (specs.ColumnIndex == 0) { // Sorting by "Name"
                std::sort(servers.begin(), servers.end(), [specs](const Server& a, const Server& b) {
                    return (specs.SortDirection == ImGuiSortDirection_Ascending) ?
                        a.name < b.name : a.name > b.name;
                    });
            } else if (specs.ColumnIndex == 1) { // Sorting by "Players"
                std::sort(servers.begin(), servers.end(), [specs](const Server& a, const Server& b) {
                    return (specs.SortDirection == ImGuiSortDirection_Ascending) ?
                        a.players < b.players : a.players > b.players;
                    });
            } else if (specs.ColumnIndex == 2) { // Sorting by "Ping"
                std::sort(servers.begin(), servers.end(), [specs](const Server& a, const Server& b) {
                    return (specs.SortDirection == ImGuiSortDirection_Ascending) ?
                        a.ping < b.ping : a.ping > b.ping;
                    });
            } else if (specs.ColumnIndex == 3) { // Sorting by "Location"
                std::sort(servers.begin(), servers.end(), [specs](const Server& a, const Server& b) {
                    return (specs.SortDirection == ImGuiSortDirection_Ascending) ?
                        a.location < b.location : a.location > b.location;
                    });
            } else if (specs.ColumnIndex == 4) { // Sorting by "Map"
                std::sort(servers.begin(), servers.end(), [specs](const Server& a, const Server& b) {
                    return (specs.SortDirection == ImGuiSortDirection_Ascending) ?
                        a.map < b.map : a.map > b.map;
                    });
            }
        }

        for (size_t i = 0; i < servers.size(); ++i) {
            const auto& server = servers[i];

            ImGui::PushID((int)i);  // Use PushID to make connect buttons unique

            ImGui::TableNextRow();

            ImGui::TableSetColumnIndex(0);
            //ImGui::Text("%s", server.name.c_str());
            ImGuiSelectableFlags selectable_flags = ImGuiSelectableFlags_SpanAllColumns | ImGuiSelectableFlags_AllowOverlap;
            if (ImGui::Selectable(server.name.c_str(), selectedServerIndex == i, selectable_flags))
            {
                selectedServerIndex = (int)i;
            }

            ImGui::TableSetColumnIndex(1);
            ImGui::Text("%d/%d", server.players, server.maxPlayers);

            ImGui::TableSetColumnIndex(2);
            ImGui::Text("%.2f ms", server.ping);

            ImGui::TableSetColumnIndex(3);
            ImGui::Text("%s", server.location.c_str());

            ImGui::TableSetColumnIndex(4);
            ImGui::Text("%s", server.map.c_str());

            ImGui::TableSetColumnIndex(5);
            if (ImGui::SmallButton("Connect")) {
                onConnect(GFrame, server);
            }

            ImGui::PopID();  // End unique ID scope for this button
        }

        ImGui::EndTable();
    }
}

// Function to refresh the server list (simulate data refresh)
void RefreshServerList(std::vector<Server>& servers) {
    // Simulate a refresh (randomize or update data here)
    for (auto& server : servers) {
        server.ping = rand() % 100 + 1;  // Random ping for demo purposes
        server.players = rand() % server.maxPlayers + 1;  // Random players count
    }
}

// Function to parse the JSON and populate the server list
std::vector<Server> ParseServersFromJson(const std::string& jsonString) {
    std::vector<Server> servers;

    try {
        nlohmann::json j = nlohmann::json::parse(jsonString);

        // The first part of the JSON is the array of servers
        for (const auto& serverJson : j[0]) {
            std::string name = serverJson.value("hostname", "Unknown");
            int players = serverJson.value("numplayers", 0);
            int maxPlayers = serverJson.value("maxplayers", 0);
            float ping = serverJson.value("ping", 0.0f);
            std::string location = serverJson.value("country", "Unknown");
            std::string map = serverJson.value("mapname", "Unknown");

            // Add server to list
            servers.push_back(Server(name, players, maxPlayers, ping, false, location, map));
        }
    }
    catch (const nlohmann::json::exception& e) {
        std::cerr << "JSON Parse Error: " << e.what() << std::endl;
    }

    return servers;
}
// Variable to determine which server list is being used
std::vector<Server>* activeServerList = &internetServers;
int selectedServerIndex = -1;  // Index of the selected server, -1 means no selection

void URenderImGuiTestWindow::Paint(FSceneNode* Frame)
{
    // 1. Show the big demo window (Most of the sample code is in ImGui::ShowDemoWindow()! You can browse its code to learn more about Dear ImGui!).
    //
    //ImGui::ShowDemoWindow();
    //return;
    // Render the server browser UI
    ImGui::Begin("Server Browser");

    // Controls: Refresh and Connect buttons
    if (ImGui::Button("Refresh Servers")) {
        // Get the JSON data from the server URL

        httplib::Client cli("http://master.333networks.com");
        //httplib::Client cli("https://master.333networks.com");
        auto res = cli.Get("/json/unreal");

        //res->status;

        GLog->Logf(TEXT("Status %i"), res->status);
        GLog->Logf(appFromAnsi(res->body.c_str()));

        if (res->status == 200)
        {
            internetServers = ParseServersFromJson(res->body);
        }

        //RefreshServerList(internetServers);
        RefreshServerList(lanServers);
    }

    if (ImGui::BeginTabBar("Tabs")) {
        if (ImGui::BeginTabItem("Internet")) {
            activeServerList = &internetServers;
            ImGui::EndTabItem();
        }

        if (ImGui::BeginTabItem("LAN")) {
            activeServerList = &lanServers;
            ImGui::EndTabItem();
        }

        ImGui::EndTabBar();
    }

    // Draw the active server list (either LAN or Internet)
    DrawServerListTable(*activeServerList, ConnectToServer, selectedServerIndex, Frame);

    ImGui::End();
}