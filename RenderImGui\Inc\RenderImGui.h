#ifndef RENDERIMGUI_API
#define RENDERIMGUI_API DLL_IMPORT
#endif

class UWindowWindow : public UObject
{
public:
	// Dimensions, offset relative to parent.
	FLOAT				WinLeft;
	FLOAT				WinTop;
	FLOAT				WinWidth;
	FLOAT				WinHeight;

	// Relationships to other windows
	UWindowWindow*		ParentWindow;			// Parent window
	UWindowWindow*		FirstChildWindow;		// First child window - bottom window first
	UWindowWindow*		LastChildWindow;		// Last child window - WinTop window first
	UWindowWindow*		NextSiblingWindow;		// sibling window - next window above us
	UWindowWindow*		PrevSiblingWindow;		// previous sibling window - next window below us
	UWindowWindow*		ActiveWindow;			// The child of ours which is currently active
	UWindowWindow*  	Root;					// The root window
	UWindowWindow*		OwnerWindow;			// Some arbitary owner window
	UWindowWindow*		ModalWindow;			// Some window we've opened modally.
};

class RENDERIMGUI_API URenderImGuiWindow : public UObject
{
	DECLARE_CLASS(URenderImGuiWindow,UObject,CLASS_Transient,RenderImGui)

	// Members:
	FLOAT WinLeft;
	FLOAT WinTop;
	FLOAT WinWidth;
	FLOAT WinHeight;
	FLOAT MouseX;
	FLOAT MouseY;
	BITFIELD bWantCaptureMouse:1 GCC_PACK(4);
	BITFIELD bWantCaptureKeyboard:1 GCC_PACK(4);
	FStringNoInit WindowTitle;

	UObject* OwnerWindow;
	UObject* EventHandler;

	FSceneNode* GFrame; // valid during Paint

	static INT NumWindows;
	static INT NumDrawn;

	// Constructors:
	URenderImGuiWindow();

	// UObject interface.
	//void Serialize( FArchive& Ar );
	//void StaticConstructor();
	//void PostLoad();
	void Destroy();
	//void Register();

	virtual void Paint(FSceneNode* Frame);
	virtual void Shown() {}
	virtual void Hidden() {}
	virtual void Closed() {}
	virtual void Resized() {}

	void Show();
	void Close();
	void Hide();
	void Resize();
	void CallEvent(const TCHAR* EventName, void* Parms);

	FString MakeUniqueName(const TCHAR* Name)
	{
		return FString::Printf(TEXT("%s##%s"), Name, GetName());
	}

	DECLARE_FUNCTION(execCreateImGuiWindow);
	DECLARE_FUNCTION(execDestroyImGuiWindow);
	DECLARE_FUNCTION(execImGuiWindowShown);
	DECLARE_FUNCTION(execImGuiWindowHidden);
	DECLARE_FUNCTION(execImGuiWindowClosed);
	DECLARE_FUNCTION(execImGuiWindowResized);
	DECLARE_FUNCTION(execPaintImGuiWindow);
	DECLARE_FUNCTION(execMouseMove);
	DECLARE_FUNCTION(execMouseClick);
	DECLARE_FUNCTION(execAddInputCharacter);
	DECLARE_FUNCTION(execAddKeyEvent);
};
