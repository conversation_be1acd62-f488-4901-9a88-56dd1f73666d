#include "Engine.h"
#include "UnRender.h"

#include "imgui.h"
#ifndef IMGUI_DISABLE
#include "imgui_impl_unreal.h"

bool bRendererSupported = true; // Set to false if the renderer is not supported
DWORD RenderFlags = PF_Modulated | PF_Translucent;

struct ImGuiTexture
{
    UTexture* UnrealTexture;
    FTextureInfo TextureInfo;
};

// Unreal data
struct ImGui_ImplUnreal_Data
{
    ImGuiTexture*       FontTexture;

    ImGui_ImplUnreal_Data() { memset((void*)this, 0, sizeof(*this)); }
};

float min3(float a, float b, float c)
{
    if (a < b && a < c) { return a; }
    return b < c ? b : c;
}

float max3(float a, float b, float c)
{
    if (a > b && a > c) { return a; }
    return b > c ? b : c;
}

#if 0
inline void SetTrans
(
    FTransTexture& tt,
    FSceneNode* Frame,
    FLOAT screen_x,
    FLOAT screen_y,
    FLOAT screen_z,
    FLOAT u,
    FLOAT v
)
{
    // FTransform:
    tt.ScreenX = screen_x;
    tt.ScreenY = screen_y;
    tt.IntY    = appFloor(tt.ScreenY);
    tt.RZ      = screen_z;

    // FTransSample:
    //tt.Light = FPlane(1.0,1.0,1.0,1.0);

    // FTransTexture:
    tt.U = u;
    tt.V = v;

    tt.Point.Z = Frame->Proj.Z / screen_z;
    tt.Point.X = (screen_x - Frame->FX15) * Frame->RProj.Z * tt.Point.Z;
    tt.Point.Y = (screen_y - Frame->FY15) * Frame->RProj.Z * tt.Point.Z;

    //ComputeClipCodes( Frame, tt );
}
#endif

// Backend data stored in io.BackendRendererUserData to allow support for multiple Dear ImGui contexts
// It is STRONGLY preferred that you use docking branch with multi-viewports (== single Dear ImGui context + multiple windows) instead of multiple Dear ImGui contexts.
static ImGui_ImplUnreal_Data* ImGui_ImplUnreal_GetBackendData()
{
    return ImGui::GetCurrentContext() ? (ImGui_ImplUnreal_Data*)ImGui::GetIO().BackendRendererUserData : nullptr;
}

// Functions
bool ImGui_ImplUnreal_Init()
{
    ImGuiIO& io = ImGui::GetIO();
    IMGUI_CHECKVERSION();
    IM_ASSERT(io.BackendRendererUserData == nullptr && "Already initialized a renderer backend!");

    // Setup backend capabilities flags
    ImGui_ImplUnreal_Data* bd = IM_NEW(ImGui_ImplUnreal_Data)();
    io.BackendRendererUserData = (void*)bd;
    io.BackendRendererName = "imgui_impl_unreal";
    io.BackendFlags |= ImGuiBackendFlags_HasMouseCursors;         // We can honor GetMouseCursor() values (optional)
    io.BackendFlags |= ImGuiBackendFlags_HasSetMousePos;          // We can honor io.WantSetMousePos requests (optional, rarely used)

    // todo: config for blacklist
    if( GRenderDevice && 
        (   appStrstr(GRenderDevice->GetName(), TEXT("D3D11RenderDevice"))
        ||  appStrstr(GRenderDevice->GetName(), TEXT("SoftwareRenderDevice")) ) )
    {
        //bRendererSupported = false;
        //RenderFlags = 0; // PF_Modulated | PF_Translucent
    }

    if( !bRendererSupported )
    {
        // Set Window background to fully opaque
        ImGui::GetStyle().Colors[ImGuiCol_WindowBg] = ImVec4(0.3f, 0.3f, 0.3f, 1.0f);  // Opaque background

        // Set Popup background to fully opaque
        ImGui::GetStyle().Colors[ImGuiCol_PopupBg] = ImVec4(0.3f, 0.3f, 0.3f, 1.0f);    // Opaque popup background

        // You can also adjust the alpha for other elements if needed
        ImGui::GetStyle().Colors[ImGuiCol_Border] = ImVec4(0.0f, 0.0f, 0.0f, 1.0f);      // Opaque borders
    }

    return true;
}

void ImGui_ImplUnreal_Shutdown()
{
    ImGui_ImplUnreal_Data* bd = ImGui_ImplUnreal_GetBackendData();
    IM_ASSERT(bd != nullptr && "No renderer backend to shutdown, or already shutdown?");
    ImGuiIO& io = ImGui::GetIO();

    ImGui_ImplUnreal_DestroyDeviceObjects();
    io.BackendRendererName = nullptr;
    io.BackendRendererUserData = nullptr;
    IM_DELETE(bd);
}

void ImGui_ImplUnreal_NewFrame(FSceneNode* Frame)
{
    ImGui_ImplUnreal_Data* bd = ImGui_ImplUnreal_GetBackendData();
    IM_ASSERT(bd != nullptr && "Context or backend not initialized! Did you call ImGui_ImplUnreal_Init()?");

    if (!bd->FontTexture)
        ImGui_ImplUnreal_CreateDeviceObjects();
    if (!bd->FontTexture)
        ImGui_ImplUnreal_CreateFontsTexture();

    ImGuiIO& io = ImGui::GetIO();

    // Setup low-level inputs, e.g. on Win32: calling GetKeyboardState(), or write to those fields from your Windows message handlers, etc.
    // (In the examples/ app this is usually done within the ImGui_ImplXXX_NewFrame() function from one of the demo Platform Backends)
    //io.DeltaTime = 1.0f/60.0f;              // set the time elapsed since the previous frame (in seconds)
    io.DeltaTime = Frame->Viewport->CurrentTime - Frame->Viewport->LastUpdateTime; // set the time elapsed since the previous frame (in seconds)
    io.DisplaySize.x = Frame->FX;             // set the current display width
    io.DisplaySize.y = Frame->FY;             // set the current display height here
}

static void ImGui_ImplUnreal_SetupRenderState(ImDrawData* draw_data, int fb_width, int fb_height)
{

}

enum { FVF_OutAll = ( FVF_OutNear | FVF_OutXMin | FVF_OutYMin | FVF_OutXMax | FVF_OutYMax ) };

// Set up the particle using the screen space vector.
//void ScreenVector

inline void ComputeClipCodes( FSceneNode* Frame, FTransTexture& tt )
{
#ifdef DEBUG_CLIPPING
	FLOAT PrjXM = Frame->PrjXM;
	FLOAT PrjXP = Frame->PrjXP;
	FLOAT PrjYM = Frame->PrjYM;
	FLOAT PrjYP = Frame->PrjYP;

	Frame->PrjXM = ADJUST_EDGE(Frame->PrjXM);
	Frame->PrjXP = ADJUST_EDGE(Frame->PrjXP);
	Frame->PrjYM = ADJUST_EDGE(Frame->PrjYM);
	Frame->PrjYP = ADJUST_EDGE(Frame->PrjYP);
#endif

	tt.ComputeOutcode( Frame );

#ifdef DEBUG_CLIPPING
	Frame->PrjXM = PrjXM;
	Frame->PrjXP = PrjXP;
	Frame->PrjYM = PrjYM;
	Frame->PrjYP = PrjYP;
#endif
}

inline void SetUV( UTexture* Texture, FTransTexture& tt, FLOAT u, FLOAT v )
{
	tt.U = FLOAT(Texture->USize - 1) * u;
	tt.V = FLOAT(Texture->VSize - 1) * v;
}

inline void SetTrans
(
	UTexture* Texture,
	FTransTexture& tt,
	FSceneNode* Frame,
	FLOAT screen_x,
	FLOAT screen_y,
	FLOAT screen_z,
	FLOAT u,
	FLOAT v
)
{
	checkSlow(Texture);
	
	// FTransform:
	tt.ScreenX = screen_x;
	tt.ScreenY = screen_y;
	tt.IntY    = appFloor(tt.ScreenY);
	tt.RZ      = screen_z;

	// FTransTexture:
	SetUV( Texture, tt, u, v );

	tt.Point.Z = Frame->Proj.Z / screen_z;
	tt.Point.X = (screen_x - Frame->FX15) * Frame->RProj.Z * tt.Point.Z;
	tt.Point.Y = (screen_y - Frame->FY15) * Frame->RProj.Z * tt.Point.Z;

	ComputeClipCodes( Frame, tt );
}

inline BYTE CombineClipCodes( FTransTexture* Verts, INT N )
{
	BYTE clip = Verts[0].Flags;
	BYTE cull = Verts[0].Flags;
	for( int c = 1; c < N; ++c )
	{
		cull &= Verts[c].Flags;
		clip |= Verts[c].Flags;
	}
	return cull ? FVF_OutAll : clip;
}

//
// Draw a triangle using DrawGouraudPolygon with specified screen coordinates, UVs, and texture
//
void DrawTriangle
(
    FSceneNode* Frame,          // Scene node for projection
    UTexture* Texture,          // Texture to apply
    FLOAT ScreenX1, FLOAT ScreenY1,  // First vertex
    FLOAT ScreenX2, FLOAT ScreenY2,  // Second vertex  
    FLOAT ScreenX3, FLOAT ScreenY3,  // Third vertex
    FLOAT U1, FLOAT V1,
    FLOAT U2, FLOAT V2,
    FLOAT U3, FLOAT V3,
    FPlane Color,               // Triangle color (R,G,B,A)
    DWORD PolyFlags             // Polygon flags (PF_Unlit, PF_Translucent, etc.)
)
{
    guard(DrawTriangle);
    
    // Validate inputs
    if (!Frame || !Frame->Viewport || !Frame->Viewport->RenDev || !Texture)
        return;
    
    // Lock the texture and get texture info
    FTextureInfo TextureInfo;
    Texture->Lock(TextureInfo, Frame->Viewport->CurrentTime, 0, Frame->Viewport->RenDev);
    
    // Create three vertices for the triangle
    FTransTexture Verts[3];
    FTransTexture* VertPtrs[3];
    
    // Initialize vertex pointers
    VertPtrs[0] = &Verts[0];
    VertPtrs[1] = &Verts[1]; 
    VertPtrs[2] = &Verts[2];
    
    // Set up a reasonable Z depth (in front of camera)
    FLOAT ScreenZ = 100.0f; // Adjust as needed for your scene
    
    // Set up vertex 0
    SetTrans(Texture, Verts[0], Frame, ScreenX1, ScreenY1, ScreenZ, U1, V1);
    Verts[0].Light = Color;
    Verts[0].Fog = FPlane(0, 0, 0, 0); // No fog
    Verts[0].Normal = FPlane(0, 0, 1, 0); // Face towards camera
    
    // Set up vertex 1
    SetTrans(Texture, Verts[1], Frame, ScreenX2, ScreenY2, ScreenZ, U2, V2);
    Verts[1].Light = Color;
    Verts[1].Fog = FPlane(0, 0, 0, 0);
    Verts[1].Normal = FPlane(0, 0, 1, 0);
    
    // Set up vertex 2
    SetTrans(Texture, Verts[2], Frame, ScreenX3, ScreenY3, ScreenZ, U3, V3);
    Verts[2].Light = Color;
    Verts[2].Fog = FPlane(0, 0, 0, 0);
    Verts[2].Normal = FPlane(0, 0, 1, 0);
    
    // Check if triangle is clipped
    BYTE ClipCode = CombineClipCodes(Verts, 3);
    if (ClipCode != FVF_OutAll)
    {
        // Draw the triangle using DrawGouraudPolygon
        Frame->Viewport->RenDev->DrawGouraudPolygon
        (
            Frame,
            TextureInfo,
            VertPtrs,
            3,              // Number of vertices
            PolyFlags,
            NULL            // No span buffer
        );
    }
    
    // Unlock the texture
    Texture->Unlock(TextureInfo);
    
    unguard;
}

void ImGui_ImplUnreal_RenderDrawData(FSceneNode* Frame, ImDrawData* draw_data)
{
    if (!bRendererSupported)
        return;

    // Avoid rendering when minimized, scale coordinates for retina displays (screen coordinates != framebuffer coordinates)
    int fb_width = (int)(draw_data->DisplaySize.x * draw_data->FramebufferScale.x);
    int fb_height = (int)(draw_data->DisplaySize.y * draw_data->FramebufferScale.y);
    if (fb_width == 0 || fb_height == 0)
        return;

    // Setup desired GL state
    ImGui_ImplUnreal_SetupRenderState(draw_data, fb_width, fb_height);

    // Will project scissor/clipping rectangles into framebuffer space
    ImVec2 clip_off = draw_data->DisplayPos;         // (0,0) unless using multi-viewports
    ImVec2 clip_scale = draw_data->FramebufferScale; // (1,1) unless using retina display which are often (2,2)

    Frame->ComputeRenderSize(); // needed for vulkan fov fix

    ImGui_ImplUnreal_Data* bd = ImGui_ImplUnreal_GetBackendData();

    UTexture* DefaultTexture = Frame->Viewport->Actor->GetLevel()->GetLevelInfo()->DefaultTexture;
    //UTexture* DefaultTexture = bd->FontTexture->UnrealTexture;
    check(DefaultTexture);

    // Get screen dimensions
    FLOAT ScreenWidth = (FLOAT)Frame->Viewport->SizeX;
    FLOAT ScreenHeight = (FLOAT)Frame->Viewport->SizeY;

    DrawTriangle(Frame, DefaultTexture,
        0, 0,
        0, 1.0*ScreenHeight,
        1.0*ScreenWidth, 0,
        // uvs
        0, 0,
        0, 1,
        1, 0,
		FPlane(1.0f, 1.0f, 1.0f, 1.0f), // White color
        PF_TwoSided
    );

    //check(DefaultTexture);
    //FTextureInfo Info;
	//DefaultTexture->Lock(Info, appSeconds(), 0, NULL);
    //Frame->Viewport->RenDev->DrawGouraudPolygon(Frame, Info, Ptr, 3, 0, NULL); // PF_TwoSided
    //DefaultTexture->Unlock(Info);
    return;

    // Render command lists
    for (int n = 0; n < draw_data->CmdListsCount; n++)
    {
        const ImDrawList* draw_list = draw_data->CmdLists[n];

        for (int cmd_i = 0; cmd_i < draw_list->CmdBuffer.Size; cmd_i++)
        {
            const ImDrawCmd* pcmd = &draw_list->CmdBuffer[cmd_i];
            const ImDrawIdx* idx_buffer = draw_list->IdxBuffer.Data + pcmd->IdxOffset;

            if (pcmd->UserCallback)
            {
                // User callback, registered via ImDrawList::AddCallback()
                // (ImDrawCallback_ResetRenderState is a special callback value used by the user to request the renderer to reset render state.)
                if (pcmd->UserCallback == ImDrawCallback_ResetRenderState)
                    ImGui_ImplUnreal_SetupRenderState(draw_data, fb_width, fb_height);
                else
                    pcmd->UserCallback(draw_list, pcmd);
            }
            else
            {
                // Project scissor/clipping rectangles into framebuffer space
                ImVec2 clip_min((pcmd->ClipRect.x - clip_off.x) * clip_scale.x, (pcmd->ClipRect.y - clip_off.y) * clip_scale.y);
                ImVec2 clip_max((pcmd->ClipRect.z - clip_off.x) * clip_scale.x, (pcmd->ClipRect.w - clip_off.y) * clip_scale.y);
                if (clip_max.x <= clip_min.x || clip_max.y <= clip_min.y)
                    continue;

                ImGuiTexture* ImGuiText = (ImGuiTexture*)pcmd->GetTexID();
                check(ImGuiText);
                check(Frame);

                // ImGui uses the first pixel for "white".
                //const ImVec2 white_uv = ImVec2(0.5f / ImGuiText->TextureInfo.USize, 0.5f / ImGuiText->TextureInfo.VSize);
                const ImVec2 white_uv = ImVec2(0.5f / ImGuiText->TextureInfo.USize, 0.5f / ImGuiText->TextureInfo.VSize);

                for (unsigned int i = 0; i + 3 <= pcmd->ElemCount; ) // Assuming triangles
                {
                    const ImDrawVert& v0 = draw_list->VtxBuffer[idx_buffer[i]];
                    const ImDrawVert& v1 = draw_list->VtxBuffer[idx_buffer[i + 1]];
                    const ImDrawVert& v2 = draw_list->VtxBuffer[idx_buffer[i + 2]];

                    const ImVec4 Col0 = ImColor(v0.col).Value;
                    const ImVec4 Col1 = ImColor(v1.col).Value;
                    const ImVec4 Col2 = ImColor(v2.col).Value;

#if 0
                    // Text is common, and is made of textured rectangles. So let's optimize for it.
                    // This assumes the ImGui way to layout text does not change.
                    if (i + 6 <= pcmd->ElemCount &&
                        idx_buffer[i + 3] == idx_buffer[i] && idx_buffer[i + 4] == idx_buffer[i + 2])
                    {
                        const ImDrawVert& v3 = draw_list->VtxBuffer[idx_buffer[i + 5]];

                        if (v0.pos.x == v3.pos.x &&
                            v1.pos.x == v2.pos.x &&
                            v0.pos.y == v1.pos.y &&
                            v2.pos.y == v3.pos.y &&
                            v0.uv.x == v3.uv.x &&
                            v1.uv.x == v2.uv.x &&
                            v0.uv.y == v1.uv.y &&
                            v2.uv.y == v3.uv.y)
                        {
                            const bool has_uniform_color =
                                v0.col == v1.col &&
                                v0.col == v2.col &&
                                v0.col == v3.col;

                            const bool has_texture =
                                v0.uv.x != white_uv.x ||
                                v0.uv.y != white_uv.y ||
                                v1.uv.x != white_uv.x ||
                                v1.uv.y != white_uv.y ||
                                v2.uv.x != white_uv.x ||
                                v2.uv.y != white_uv.y ||
                                v3.uv.x != white_uv.x ||
                                v3.uv.y != white_uv.y;

                            if (has_uniform_color && has_texture)
                            {
                                const ImVec2 v0c = ImVec2(Max(v0.pos.x, clip_min.x), Max(v0.pos.y, clip_min.y));
                                const ImVec2 v2c = ImVec2(Min(v2.pos.x, clip_max.x), Min(v2.pos.y, clip_max.y));

                                if (v2c.x > v0c.x && v2c.y > v0c.y)
                                {
                                    //GLog->Logf("DrawTile %.2f %.2f; %.2f %.2f %.2f %.2f; %.2f %.2f", v0c.x, v0c.y, v2c.x - v0c.x, v2c.y - v0c.y, v0.uv.x, v0.uv.y, v2.uv.x - v0.uv.x, v2.uv.y - v0.uv.y);
                                    Frame->Viewport->RenDev->DrawTile(
                                        Frame, ImGuiText->TextureInfo,
                                        v0c.x, v0c.y,
                                        v2c.x - v0c.x, v2c.y - v0c.y,
                                        v0.uv.x, v0.uv.y,
                                        v2.uv.x - v0.uv.x, v2.uv.y - v0.uv.y,
                                        NULL, 1.0, FPlane(Col0.x,Col0.y,Col0.z,Col0.w),
                                        FPlane(0, 0, 0, 0), PF_TwoSided | RenderFlags
                                    );
                                }

                                i += 6;
                                continue;
                            }
                        }
                    }

                    // A lot of the big stuff are uniformly colored rectangles,
                    // so we can save a lot of CPU by detecting them:
                    if (i + 6 <= pcmd->ElemCount)
                    {
                        const ImDrawVert& v3 = draw_list->VtxBuffer[idx_buffer[i + 3]];
                        const ImDrawVert& v4 = draw_list->VtxBuffer[idx_buffer[i + 4]];
                        const ImDrawVert& v5 = draw_list->VtxBuffer[idx_buffer[i + 5]];

                        ImVec2 min, max;
                        min.x = min3(v0.pos.x, v1.pos.x, v2.pos.x);
                        min.y = min3(v0.pos.y, v1.pos.y, v2.pos.y);
                        max.x = max3(v0.pos.x, v1.pos.x, v2.pos.x);
                        max.y = max3(v0.pos.y, v1.pos.y, v2.pos.y);

                        // Not the prettiest way to do this, but it catches all cases
                        // of a rectangle split into two triangles.
                        // TODO: Stop it from also assuming duplicate triangles is one rectangle.
                        if ((v0.pos.x == min.x || v0.pos.x == max.x) &&
                            (v0.pos.y == min.y || v0.pos.y == max.y) &&
                            (v1.pos.x == min.x || v1.pos.x == max.x) &&
                            (v1.pos.y == min.y || v1.pos.y == max.y) &&
                            (v2.pos.x == min.x || v2.pos.x == max.x) &&
                            (v2.pos.y == min.y || v2.pos.y == max.y) &&
                            (v3.pos.x == min.x || v3.pos.x == max.x) &&
                            (v3.pos.y == min.y || v3.pos.y == max.y) &&
                            (v4.pos.x == min.x || v4.pos.x == max.x) &&
                            (v4.pos.y == min.y || v4.pos.y == max.y) &&
                            (v5.pos.x == min.x || v5.pos.x == max.x) &&
                            (v5.pos.y == min.y || v5.pos.y == max.y))
                        {
                            const bool has_uniform_color =
                                v0.col == v1.col &&
                                v0.col == v2.col &&
                                v0.col == v3.col &&
                                v0.col == v4.col &&
                                v0.col == v5.col;

                            const bool has_texture =
                                v0.uv.x != white_uv.x ||
                                v0.uv.y != white_uv.y ||
                                v1.uv.x != white_uv.x ||
                                v1.uv.y != white_uv.y ||
                                v2.uv.x != white_uv.x ||
                                v2.uv.y != white_uv.y ||
                                v3.uv.x != white_uv.x ||
                                v3.uv.y != white_uv.y ||
                                v4.uv.x != white_uv.x ||
                                v4.uv.y != white_uv.x ||
                                v5.uv.x != white_uv.x ||
                                v5.uv.y != white_uv.x;

                            min.x = Max(min.x, pcmd->ClipRect.x);
                            min.y = Max(min.y, pcmd->ClipRect.y);
                            max.x = Min(max.x, pcmd->ClipRect.z - 0.5f);
                            max.y = Min(max.y, pcmd->ClipRect.w - 0.5f);

                            if (max.x < min.x || max.y < min.y) { i+=6; continue; } // Completely clipped

                            if (has_uniform_color && !has_texture)
                            {
                                Frame->Viewport->RenDev->DrawTile(
                                    Frame, ImGuiText->TextureInfo,
                                    min.x, min.y,
                                    max.x - min.x, max.y - min.y,
                                    v0.uv.x, v0.uv.y,
                                    v2.uv.x - v0.uv.x, v2.uv.y - v0.uv.y,
                                    NULL, 1.0, FPlane(Col0.x,Col0.y,Col0.z,Col0.w),
                                    FPlane(0, 0, 0, 0), PF_TwoSided | RenderFlags
                                );
                                i += 6;
                                continue;
                            }
                        }
                    }
#endif

                    const ImVec2 v0c = ImVec2(Clamp(v0.pos.x, clip_min.x, clip_max.x), Clamp(v0.pos.y, clip_min.y, clip_max.y));
                    const ImVec2 v1c = ImVec2(Clamp(v1.pos.x, clip_min.x, clip_max.x), Clamp(v1.pos.y, clip_min.y, clip_max.y));
                    const ImVec2 v2c = ImVec2(Clamp(v2.pos.x, clip_min.x, clip_max.x), Clamp(v2.pos.y, clip_min.y, clip_max.y));

                    //FTransTexture* Samples = New<FTransTexture>(GMem,3);
                    static FTransTexture Samples[3];

                    //SetTrans(Samples[0], Frame, v0c.x, v0c.y, 1.0, v0.uv.x, v0.uv.y);
                    //SetTrans(Samples[1], Frame, v1c.x, v1c.y, 1.0, v1.uv.x, v1.uv.y);
                    //SetTrans(Samples[2], Frame, v2c.x, v2c.y, 1.0, v2.uv.x, v2.uv.y);

					Samples[0].Light = FPlane(Col0.x,Col0.y,Col0.z,Col0.w);
                    Samples[1].Light = FPlane(Col1.x,Col1.y,Col1.z,Col1.w);
                    Samples[2].Light = FPlane(Col2.x,Col2.y,Col2.z,Col2.w);

                    FTransTexture* Ptr[3] = { &Samples[0], &Samples[1], &Samples[2] };

                    //GLog->Logf("DrawGouraudPolygon: X (%.2f, %.2f, %.2f), Y (%.2f, %.2f, %.2f), Z (%.2f, %.2f, %.2f)", Samples[0].Point.X, Samples[0].Point.Y, Samples[0].Point.Z, Samples[1].Point.X, Samples[1].Point.Y, Samples[1].Point.Z, Samples[2].Point.X, Samples[2].Point.Y, Samples[2].Point.Z);
                    //GLog->Logf("DrawGouraudPolygon TextureInfo: X %i, Y %i, Format %i", Texture->TextureInfo.USize, Texture->TextureInfo.VSize, Texture->TextureInfo.Format);
                    
                    Frame->Viewport->RenDev->DrawGouraudPolygon(Frame, ImGuiText->TextureInfo, Ptr, 3, PF_TwoSided | RenderFlags, NULL);

                    i += 3;
                }
            } 
        }
    }
}

void CreateMipsNew( UTexture* Texture )
{
	guard(CreateMipsNew);

    check( Texture->Format==TEXF_RGB8 || Texture->Format==TEXF_RGBA8 || Texture->Format==TEXF_RGBA7 );

    TArray<FMipmap>& Mips = Texture->Mips;
    BYTE UBits = Texture->UBits;
    BYTE VBits = Texture->VBits;

	// Empty any mipmaps.
	if( Mips.Num() > 1 )
		Mips.Remove( 1, Mips.Num()-1 );

	// Allocate mipmaps.
	while( UBits-Mips.Num()>=0 || VBits-Mips.Num()>=0 )
	{
		INT Num = Mips.Num();
		INT MipUBits = Max(UBits - Num, 0);
		INT MipVBits = Max(VBits - Num, 0);
		new(Mips)FMipmap( MipUBits, MipVBits, (1 << MipUBits) * (1 << MipVBits) * 4 );
	}

	// Build each mip from the next-larger mip.
	for( INT MipLevel=1; MipLevel<Mips.Num(); MipLevel++ )
	{
		FMipmap&  Src        = Mips(MipLevel-1);
		FMipmap&  Dest       = Mips(MipLevel  );
		INT       ThisUTile  = Src.USize;
		INT       ThisVTile  = Src.VSize;

		// Source coordinate masking important for degenerate mipmap sizes.
		DWORD MaskU = (ThisUTile-1);
		DWORD MaskV = (ThisVTile-1);
		INT   UD    = (1 & MaskU);
		INT   VD    = (1 & MaskV)*ThisUTile;

		// Non-masked mipmap.
		for( INT V=0; V<Dest.VSize; V++ )
		{
			for( INT U=0; U<Dest.USize; U++)
			{
				// Get 4 pixels from a one-higher-level mipmap.
				INT TexCoord = U*2 + V*2*ThisUTile;
                FPlane C(0,0,0,0);

                FColor* ColorSrc = (FColor*)&Src.DataArray(0);
#if 0
				C += ColorSrc[ TexCoord +  0 +  0 ].Plane();
				C += ColorSrc[ TexCoord + UD +  0 ].Plane();
				C += ColorSrc[ TexCoord +  0 + VD ].Plane(); 
				C += ColorSrc[ TexCoord + UD + VD ].Plane();
				FColor AvgColor = FColor( C/4.0f );
#else
                FColor AvgColor = ColorSrc[TexCoord];
#endif

                FColor* ColorSrcDest = (FColor*)&Dest.DataArray(0);
                ColorSrcDest[V*Dest.USize+U] = AvgColor;
			}
		}
	}
	unguard;
}

bool ImGui_ImplUnreal_CreateFontsTexture()
{
    // Build texture atlas
    ImGuiIO& io = ImGui::GetIO();
    ImGui_ImplUnreal_Data* bd = ImGui_ImplUnreal_GetBackendData();
    unsigned char* pixels;
    int width, height;

    // Clear any existing fonts in the font atlas
    //io.Fonts->Clear();

    // Font configuration structure, for !bRendererSupported
    //ImFontConfig fontConfig;
    //fontConfig.OversampleH = 1;  // Reduce horizontal oversampling (helps with pixelated look)
    //fontConfig.OversampleV = 1;  // Reduce vertical oversampling (helps with pixelated look)
    //fontConfig.PixelSnapH = true;  // Make sure the font snaps to pixels (helps prevent blurry text)

    //io.Fonts->AddFontFromFileTTF("Dauphin-Regular.ttf", 16.0f, nullptr, io.Fonts->GetGlyphRangesDefault());
    //io.Fonts->AddFontFromFileTTF("Roboto-Regular.ttf", 16.0f, nullptr, io.Fonts->GetGlyphRangesCyrillic());
    io.Fonts->AddFontFromFileTTF("OpenSans-Regular.ttf", 18.0f);

    // Set desired texture width (e.g., 256px)
    io.Fonts->TexDesiredWidth = 256;  // Set max width to 256px

    // Add the default font (this includes the entire character set, not just ASCII)
    //io.Fonts->AddFontDefault();

    // Build the font atlas (this will pack the glyphs with the specified width)
    io.Fonts->Build();  // <-- This is needed to actually generate the font atlas with the desired configuration

    UTexture* Texture = CastChecked<UTexture>(UObject::StaticConstructObject(UTexture::StaticClass()));
    Texture->SetFlags(RF_Native); // prevent the texture from being garbage collected

    if( 1 || bRendererSupported )
    {
        io.Fonts->GetTexDataAsRGBA32(&pixels, &width, &height);
        
        check(width <= 256);
        check(height <= 256);

        Texture->Format = TEXF_RGBA8;
        Texture->Init(width, height);
        Texture->PostLoad();

        appMemcpy( &Texture->Mips(0).DataArray(0), pixels, width * height * 4 );
    }
    else
    {
        io.Fonts->GetTexDataAsRGBA32(&pixels, &width, &height);

        check(width <= 256);
        check(height <= 256);

        Texture->Format = TEXF_RGBA7;
        Texture->Init(width, height);
        Texture->PostLoad();

        //Texture->Palette->HasAlphaChannel = 0;

        appMemcpy( &Texture->Mips(0).DataArray(0), pixels, width * height * 4  );
    }

    // not needed with TexDesiredWidth
    //CreateMipsNew(Texture);
    //Texture->CreateColorRange();

    ImGuiTexture* ImGuiTex = new ImGuiTexture();
    ImGuiTex->UnrealTexture = Texture;
    Texture->Lock(ImGuiTex->TextureInfo, appSeconds(), 0, NULL);

    // allow us to use texture coordinates that are 0-1 range
    ImGuiTex->TextureInfo.UScale /= ImGuiTex->TextureInfo.USize;
    ImGuiTex->TextureInfo.VScale /= ImGuiTex->TextureInfo.VSize;

    bd->FontTexture = ImGuiTex;

    // Store our identifier
    io.Fonts->SetTexID((ImTextureID)(intptr_t)bd->FontTexture);

    return true;
}

void ImGui_ImplUnreal_DestroyFontsTexture()
{
    ImGuiIO& io = ImGui::GetIO();
    ImGui_ImplUnreal_Data* bd = ImGui_ImplUnreal_GetBackendData();
    if (bd->FontTexture)
    {
        delete bd->FontTexture->UnrealTexture;
		delete bd->FontTexture;
        io.Fonts->SetTexID(0);
        bd->FontTexture = 0;
    }
}

bool ImGui_ImplUnreal_CreateDeviceObjects()
{
    return ImGui_ImplUnreal_CreateFontsTexture();
}

void ImGui_ImplUnreal_DestroyDeviceObjects()
{
    ImGui_ImplUnreal_DestroyFontsTexture();
}

//-----------------------------------------------------------------------------

#endif // #ifndef IMGUI_DISABLE
