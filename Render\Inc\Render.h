/*=============================================================================
	RenderPrivate.h: Rendering package private header.
	Copyright 1997-1999 Epic Games, Inc. All Rights Reserved.
=============================================================================*/

/*----------------------------------------------------------------------------
	API.
----------------------------------------------------------------------------*/

#ifndef RENDER_API
	#define RENDER_API DLL_IMPORT
#endif

/*------------------------------------------------------------------------------------
	Dependencies.
------------------------------------------------------------------------------------*/

#include "Engine.h"
#include "UnRender.h"
#include "UnStat.h"
#include "Amd3d.h"

// todo: temp, remove stats
//#define STATS 0
#undef STAT
#define STAT(x) {}

/*------------------------------------------------------------------------------------
	Render package private.
------------------------------------------------------------------------------------*/

#include "UnSpan.h"
#include "UnCoverage.h"

#define MAKELABEL(A,B,C,D) A##B##C##D

struct FBspDrawList
{
	INT 			iNode;
	INT				iSurf;
	INT				iZone;
	INT				Key;
	DWORD			PolyFlags;
	FSpanBuffer		Span;
	AZoneInfo*		Zone;
	FBspDrawList*	Next;
	FBspDrawList*	SurfNext;
	FActorLink*		Volumetrics;
	FSavedPoly*		Polys;
	FActorLink*		SurfLights;
};

// testing
struct FLightData
{
	FPlane Normal, Light, Fog;
};

//
// Class encapsulating the dynamic lighting subsystem.
//
class RENDER_API FLightManagerBase
{
public:
	virtual void Init()=0;
	virtual void Exit()=0;
	virtual DWORD SetupForActor( FSceneNode* Frame, AActor* Actor, struct FVolActorLink* LeafLights, FActorLink* Volumetrics )=0;
	virtual void SetupForSurf( FSceneNode* Frame, FCoords& FacetCoords, FBspDrawList* Draw, FTextureInfo*& LightMap, FTextureInfo*& FogMap, UBOOL Merged )=0;
	virtual void FinishSurf()=0;
	virtual void FinishActor()=0;
	virtual void StartFrame( FSceneNode* Frame )=0;
	virtual void EndFrame( FSceneNode* Frame, URenderDevice* RenDev )=0;
	virtual void EnableActorShadows( bool bNewShouldDrawShadows )=0;
	virtual void LightAndFog( FLightData& FLightData, FVector& Point, DWORD PolyFlags, FLighting& Lighting )=0;
	virtual void LightParticleSystem( FSceneNode* Frame, AParticleFX* ParticleFX ) = 0;
};

/*------------------------------------------------------------------------------------
	Links.
------------------------------------------------------------------------------------*/

//
// Linked list of actors with volumetric flag.
//
struct FVolActorLink
{
	// Variables.
	FVector			Location;
	AActor*			Actor;
	FVolActorLink*	Next;
	UBOOL			Volumetric;

	// Functions.
	FVolActorLink( FCoords& Coords, AActor* InActor, FVolActorLink* InNext, UBOOL InVolumetric )
	:	Location	( InActor->Location.TransformPointBy( Coords ) )
	,	Actor		( InActor )
	,	Next		( InNext )
	,	Volumetric	( InVolumetric )
	{}
	FVolActorLink( FVolActorLink& Other, FVolActorLink* InNext )
	:	Location	( Other.Location )
	,	Actor		( Other.Actor )
	,	Volumetric	( Other.Volumetric )
	,	Next		( InNext )
	{}
};
extern AActor* Consider[120];
extern INT NumConsider;

/*------------------------------------------------------------------------------------
	Dynamic Bsp contents.
------------------------------------------------------------------------------------*/

struct FDynamicItem
{
	// Variables.
	FDynamicItem*	FilterNext;
	void*			Unk;
	FLOAT			Z;

	// Functions.
	FDynamicItem() {}
	FDynamicItem( INT iNode ) {}
	virtual void Filter( UViewport* Viewport, FSceneNode* Frame, INT iNode, INT Outside ) {}
	virtual void PreRender( UViewport* Viewport, FSceneNode* Frame, FSpanBuffer* SpanBuffer, INT iNode, FVolActorLink* Volumetrics ) {}
};

struct FDynamicSprite : public FDynamicItem
{
	// Variables.
	FSpanBuffer*	SpanBuffer;
	FDynamicSprite*	RenderNext;
	FTransform		ProxyVerts[4];
	FVector			UnkVectors[3];
	AActor*			Actor;
	INT				X1, Y1;
	INT				X2, Y2;
	FLOAT 			ScreenX, ScreenY;
	FLOAT			Persp;
	FActorLink*		Volumetrics;
	FVolActorLink*	LeafLights;

	// Functions.
	FDynamicSprite( FSceneNode* Frame, INT iNode, AActor* Actor );
	FDynamicSprite( INT iNode, AActor* InActor ) : FDynamicItem( iNode ), Actor( InActor ), SpanBuffer( NULL ), Volumetrics( NULL ), LeafLights( NULL ) {}
	FDynamicSprite( AActor* InActor ) : Actor( InActor ), SpanBuffer( NULL ), Volumetrics( NULL ), LeafLights( NULL ) {}
	UBOOL Setup( FSceneNode* Frame );
};

struct FDynamicChunk : public FDynamicItem
{
	// Variables.
	FRasterPoly*	Raster;
	FDynamicSprite* Sprite;

	// Functions.
	FDynamicChunk( INT iNode, FDynamicSprite* InSprite, FRasterPoly* InRaster );
	void Filter( UViewport* Viewport, FSceneNode* Frame, INT iNode, INT Outside );
};

struct FDynamicFinalChunk : public FDynamicItem
{
	// Variables.
	FRasterPoly*	Raster;
	FDynamicSprite* Sprite;

	// Functions.
	FDynamicFinalChunk( INT iNode, FDynamicSprite* InSprite, FRasterPoly* InRaster, INT IsBack );
	void PreRender( UViewport* Viewport,  FSceneNode* Frame, FSpanBuffer* SpanBuffer, INT iNode, FVolActorLink* Volumetrics );
};

struct FDynamicLight : public FDynamicItem
{
	// Variables.
	AActor*			Actor;
	UBOOL			IsVol;
	UBOOL			HitLeaf;

	// Functions.
	FDynamicLight( INT iNode, AActor* Actor, UBOOL IsVol, UBOOL InHitLeaf );
	void Filter( UViewport* Viewport, FSceneNode* Frame, INT iNode, INT Outside );
};

struct FDynamicSysChild : public FDynamicSprite
{
	// Functions.
	FDynamicSysChild( FSceneNode* Frame, INT iNode, AActor* Actor );
};

struct FDynamicSysParent : public FDynamicSprite
{
	// Variables.
	TArray<FDynamicSysChild*>	Children;

	// Functions.
	FDynamicSysParent( FSceneNode* Frame, INT iNode, AActor* Actor );
	UBOOL Setup( FSceneNode* Frame );
};

/*------------------------------------------------------------------------------------
	Globals.
------------------------------------------------------------------------------------*/

RENDER_API extern FLightManagerBase* GLightManager;
#if !UNDYING_MEM
RENDER_API extern FMemStack GDynMem, GSceneMem;
#endif

/*------------------------------------------------------------------------------------
	Random numbers.
------------------------------------------------------------------------------------*/

// Random number subsystem.
// Tracks a list of set random numbers.
class FGlobalRandomsBase
{
public:
	// Functions.
	virtual void Init()=0; // Initialize subsystem.
	virtual void Exit()=0; // Shut down subsystem.
	virtual void Tick(FLOAT TimeSeconds)=0; // Mark one unit of passing time.

	// Inlines.
	FLOAT RandomBase( int i ) {return RandomBases[i & RAND_MASK]; }
	FLOAT Random(     int i ) {return Randoms    [i & RAND_MASK]; }

protected:
	// Constants.
	enum {RAND_CYCLE = 16       }; // Number of ticks for a complete cycle of Randoms.
	enum {N_RANDS    = 256      }; // Number of random numbers tracked, guaranteed power of two.
	enum {RAND_MASK  = N_RANDS-1}; // Mask so that (i&RAND_MASK) is a valid index into Randoms.

	// Variables.
	static FLOAT RandomBases	[N_RANDS]; // Per-tick discontinuous random numbers.
	static FLOAT Randoms		[N_RANDS]; // Per-tick continuous random numbers.
};
extern FGlobalRandomsBase *GRandoms;

/*------------------------------------------------------------------------------------
	Fast approximate math code.
------------------------------------------------------------------------------------*/

#define APPROX_MAN_BITS 10		/* Number of bits of approximate square root mantissa, <=23 */
#define APPROX_EXP_BITS 9		/* Number of bits in IEEE exponent */

extern FLOAT SqrtManTbl[2<<APPROX_MAN_BITS];
extern FLOAT DivSqrtManTbl[1<<APPROX_MAN_BITS],DivManTbl[1<<APPROX_MAN_BITS];
extern FLOAT DivSqrtExpTbl[1<<APPROX_EXP_BITS],DivExpTbl[1<<APPROX_EXP_BITS];

//
// Macro to look up from a power table.
//
#if ASM
#define POWER_ASM(ManTbl,ExpTbl)\
	__asm\
	{\
		/* Here we use the identity sqrt(a*b) = sqrt(a)*sqrt(b) to perform\
		** an approximate floating point square root by using a lookup table\
		** for the mantissa (a) and the exponent (b), taking advantage of the\
		** ieee floating point format.\
		*/\
		__asm mov  eax,[F]									/* get float as int                   */\
		__asm shr  eax,(32-APPROX_EXP_BITS)-APPROX_MAN_BITS	/* want APPROX_MAN_BITS mantissa bits */\
		__asm mov  ebx,[F]									/* get float as int                   */\
		__asm shr  ebx,32-APPROX_EXP_BITS					/* want APPROX_EXP_BITS exponent bits */\
		__asm and  eax,(1<<APPROX_MAN_BITS)-1				/* keep lowest 9 mantissa bits        */\
		__asm fld  DWORD PTR ManTbl[eax*4]					/* get mantissa lookup                */\
		__asm fmul DWORD PTR ExpTbl[ebx*4]					/* multiply by exponent lookup        */\
		__asm fstp [F]										/* store result                       */\
	}\
	return F;
//
// Fast floating point power routines.
// Pretty accurate to the first 10 bits.
// About 12 cycles on the Pentium.
//
inline FLOAT DivSqrtApprox(FLOAT F) {POWER_ASM(DivSqrtManTbl,DivSqrtExpTbl);}
inline FLOAT DivApprox    (FLOAT F) {POWER_ASM(DivManTbl,    DivExpTbl    );}
inline FLOAT SqrtApprox   (FLOAT F)
{
	__asm
	{
		mov  eax,[F]                        // get float as int.
		shr  eax,(23 - APPROX_MAN_BITS) - 2 // shift away unused low mantissa.
		mov  ebx,[F]						// get float as int.
		and  eax, ((1 << (APPROX_MAN_BITS+1) )-1) << 2 // 2 to avoid "[eax*4]".
		and  ebx, 0x7F000000				// 7 bit exp., wipe low bit+sign.
		shr  ebx, 1							// exponent/2.
		mov  eax,DWORD PTR SqrtManTbl [eax]	// index hi bit is exp. low bit.
		add  eax,ebx						// recombine with exponent.
		mov  [F],eax						// store.
	}
	return F;								// compiles to fld [F].
}
#else
inline FLOAT DivSqrtApprox(FLOAT F) {return 1.0/appSqrt(F);}
inline FLOAT DivApprox    (FLOAT F) {return 1.0/F;}
inline FLOAT SqrtApprox   (FLOAT F) {return appSqrt(F);}
#endif

// undying functions
// High level primitive drawing.
inline void Draw3DLine(FSceneNode* Frame, FPlane Color, DWORD LineFlags, const FVector& Start, const FVector& End)
{
	Frame->Viewport->RenDev->Draw3DLine(Frame, Color, LineFlags, Start, End);
}

/*------------------------------------------------------------------------------------
	URender.
------------------------------------------------------------------------------------*/

//
// Software rendering subsystem.
//
class RENDER_API URender : public URenderBase
{
	DECLARE_CLASS(URender,URenderBase,CLASS_Config,Render)

	// Friends.
	friend class  FGlobalSpanTextureMapper;
	friend struct FDynamicItem;
	friend struct FDynamicSprite;
	friend struct FDynamicChunk;
	friend struct FDynamicFinalChunk;
	friend struct FDynamicLight;
	friend class  FLightManager;
	friend void RenderSubsurface
	(
		UViewport*		Viewport,
		FSceneNode*	Frame,
		UTexture*		Texture,
		FSpanBuffer*	Span,
		FTransTexture*	Pts,
		DWORD			PolyFlags,
		INT				SubCount
	);

	// obsolete!!
	enum EDrawRaster
	{
		DRAWRASTER_Flat				= 0,	// Flat shaded
		DRAWRASTER_Normal			= 1,	// Normal texture mapped
		DRAWRASTER_Masked			= 2,	// Masked texture mapped
		DRAWRASTER_Blended			= 3,	// Blended texture mapped
		DRAWRASTER_Fire				= 4,	// Fire table texture mapped
		DRAWRASTER_MAX				= 5,	// First invalid entry
	};

	// Constructor.
	URender();
	void StaticConstructor();

	// UObject interface.
	void Destroy();

	// URenderBase interface.
	void Init( UEngine* InEngine );
	UBOOL Exec( const TCHAR* Cmd, FOutputDevice& Ar=*GLog );
	void PreRender( FSceneNode* Frame );
	void PostRender( FSceneNode* Frame );
	void DrawWorld( FSceneNode* Frame );
	UBOOL Deproject( FSceneNode* Frame, INT ScreenX, INT ScreenY, FVector& V );
	UBOOL Project( FSceneNode* Frame, const FVector& V, FLOAT& ScreenX, FLOAT& ScreenY, FLOAT* Scale );
	void DrawActor( FSceneNode* Frame, AActor* Actor );
	void GetVisibleSurfs( UViewport* Viewport, TArray<INT>& iSurfs );
	void OccludeBsp( FSceneNode* Frame );
	void SetupDynamics( FSceneNode* Frame, AActor* Exclude );
	UBOOL BoundVisible( FSceneNode* Frame, FBox* Bound, FSpanBuffer* SpanBuffer, FCoverageBuffer* CoverageBuffer, FScreenBounds& Results );
	void GlobalLighting( UBOOL Realtime, AActor* Owner, FLOAT& Brightness, FPlane& Color );
	FSceneNode* CreateMasterFrame( UViewport* Viewport, FVector Location, FRotator Rotation, FScreenBounds* Bounds );
	FSceneNode* CreateChildFrame( FSceneNode* Frame, FSpanBuffer* Span, FCoverageMask* CoverageMask, ULevel* Level, INT iSurf, INT iZone, FLOAT Mirror, const FPlane& NearClip, const FCoords& Coords, FScreenBounds* Bounds );
	void FinishMasterFrame();
	void DrawCircle( FSceneNode* Frame, FPlane Color, DWORD LineFlags, FVector& Location, FLOAT Radius, UBOOL bScaleRadiusByZoom = 0 );
	void Draw3DCircle( FSceneNode* Frame, FPlane Color, DWORD LineFlags, FVector& Location, const FVector& );
	void Draw3DEllipse( FSceneNode* Frame, FPlane Color, DWORD LineFlags, FVector& Location, const FVector&, const FVector& );
	void DrawBox( FSceneNode* Frame, FPlane Color, DWORD LineFlags, FVector Min, FVector Max );
	void DrawBox( FSceneNode* Frame, FPlane Color, DWORD LineFlags, const FBox& Box, const FCoords& Coords );
	void DrawCylinder( FSceneNode* Frame, FPlane Color, DWORD LineFlags, const FCylinder& Cylinder, const FCoords& Place );
	void DrawCoords( FSceneNode* Frame, float, DWORD, const FCoords&, float );
	void Precache( UViewport* Viewport );

	// Coverage buffer.
	UBOOL ShouldUseCoverageBuffer;

	// Dynamics cache.
	FVolActorLink* FirstVolumetric;

	// Scene frames.
	enum {MAX_FRAME_RECURSION=4};

	// Dynamic lighting.
	static INT				MaxSurfLights;
	static INT				MaxLeafLights;
	static FActorLink**		SurfLights;
	static FVolActorLink**	LeafLights;
	static FVector			VolCross[32];

	// Variables.
	UBOOL					Toggle;
	UBOOL					LeakCheck;
	UBOOL                   WireShow;
	UBOOL                   BlendShow;
	UBOOL                   BoneShow;
	FLOAT					GlobalMeshLOD;
	FLOAT					GlobalShapeLOD;
	FLOAT					GlobalShapeLODAdjust;
	INT                     ShapeLODMode;
	FLOAT                   ShapeLODFix;
	INT						FastMeshMode;

	// Timing.
	FTime					LastEndTime;
	FTime					StartTime;
	FTime					EndTime;
	DWORD					NodesDraw;
	DWORD					PolysDraw;

#if !UNDYING_MEM
	// Scene.
	FMemMark				SceneMark, MemMark, DynMark;
	INT						SceneCount;
#endif

	// Which stats to display.
	UBOOL NetStats;
	UBOOL FpsStats;
	UBOOL GlobalStats;
	UBOOL MeshStats;
	UBOOL RenderStats;
	UBOOL RenderDevStats;
	UBOOL ActorStats;
	UBOOL ZoneStats;
	UBOOL LightStats;
	UBOOL OcclusionStats;
	UBOOL WorldStats;
	UBOOL GameStats;
	UBOOL SoftStats;
	UBOOL CacheStats;
	UBOOL IllumStats;
	UBOOL ShadowStats;
	UBOOL HardwareStats;
	UBOOL EARIStats;
	UBOOL EARIDetails;
	UBOOL ParticleStats;
	UBOOL AnimStats;
	UBOOL ScriptStats;
	UBOOL PhysicsStats;
	UBOOL QuietDump;

	// old: Stat line counter.
	INT   StatLine;
	FLOAT AverageTime;

	// OccludeBsp dynamics.
	struct FDynamicsCache
	{
		FDynamicItem* Dynamics[3];
	};

	INT NumDynamicsCachePtrs;
	FDynamicsCache** DynamicsCachePtrs;

	static TArray<FDynamicsCache> DynamicsCache;
	struct FStampedPoint
	{
		FTransform* Point;
		DWORD		Stamp;
	};
	static TArray<FStampedPoint> PointCache;
	static DWORD Stamp;
	FDynamicItem*& Dynamic( INT iNode, INT i )
	{
		return DynamicsCache(iNode).Dynamics[i];
	}
	void AllocDynamicsCache( UModel* Model );

	// Implementation.
	void OccludeFrame( FSceneNode* Frame );
	void DrawFrame( FSceneNode* Frame );
	void LeafVolumetricLighting( FSceneNode* Frame, UModel* Model, INT iLeaf );
	INT ClipBspSurf( INT iNode, FTransform**& OutPts );
	INT AMD3DClipBspSurf( INT iNode, FTransform**& OutPts );
	INT ClipTexPoints( FSceneNode* Frame, FTransTexture* InPts, FTransTexture* OutPts, INT Num0 );
	void DrawActorSprite( FSceneNode* Frame, FDynamicSprite* Sprite );
	void DrawParticleSystem( FSceneNode* Frame, FDynamicSprite* Sprite );
	void DrawMesh( FSceneNode* Frame, AActor* Owner, AActor* LightSink, FSpanBuffer*, AZoneInfo* Zone, const FCoords& Coords, FVolActorLink* LeafLights, FActorLink* Volumetrics, DWORD PolyFlags );
	void ShowStat( FSceneNode* Frame, const TCHAR* Fmt, ... );
	void DrawStats( FSceneNode* Frame );
#ifndef NODECALS
	INT ClipDecal( FSceneNode* Frame, FDecal *Decal, UModel* Model, FBspSurf* Surf, FSavedPoly* Poly, FTransTexture**& DecalPoints, INT& NumPts );
#endif

	// placed here for now
	static INT MaxVertices;
	static void DrawTriangles(FSceneNode* Frame, FTextureInfo& Texture, FTransTexture** Pts, INT NumPts, _WORD* Indices, INT NumIndices, DWORD PolyFlags, FSpanBuffer* SpanBuffer);
};
extern RENDER_API URender* GRender;

#define AUTO_INITIALIZE_REGISTRANTS_RENDER \
	URender::StaticClass();

/*------------------------------------------------------------------------------------
	Triangle Drawing Function Declaration
------------------------------------------------------------------------------------*/

//
// Draw a simple triangle on the screen using DrawGouraudPolygon
//
void DrawTriangleOnScreen
(
	FSceneNode* Frame,			// Scene node for projection
	UTexture* Texture,			// Texture to apply (can be NULL for solid color)
	FLOAT CenterX,				// Screen X center position
	FLOAT CenterY,				// Screen Y center position
	FLOAT Size,					// Triangle size
	FPlane Color,				// Triangle color (R,G,B,A)
	DWORD PolyFlags				// Polygon flags (PF_Unlit, PF_Translucent, etc.)
);

/*------------------------------------------------------------------------------------
	The End.
------------------------------------------------------------------------------------*/
