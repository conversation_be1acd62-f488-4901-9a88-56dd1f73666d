cmake_minimum_required(VERSION 3.16.0)

project(DWIUnreal)

#set(CMAKE_CXX_STANDARD 98)
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Options
option(UNDYING_COMPAT "Undying binary compatibility" OFF)
option(ENA<PERSON>E_UNICODE "Enable UNICODE and _UNICODE definitions" OFF)
option(BUILD_STATIC "Static build" OFF)

if(UNDYING_COMPAT)
    if(ENABLE_UNICODE OR BUILD_STATIC)
        message(WARNING "ENABLE_UNICODE and BUILD_STATIC are incompatible with UNDYING_COMPAT. They will be turned OFF.")
    endif()

    # Forcefully turn them OFF
    set(ENABLE_UNICODE OFF CACHE BOOL "Enable UNICODE and _UNICODE definitions" FORCE)
    set(BUILD_STATIC OFF CACHE BOOL "Static build" FORCE)
endif()

# force settings here
set(UNDYING_COMPAT OFF CACHE BOOL "Undying binary compatibility" FORCE)
set(ENABLE_UNICODE OFF CACHE BOOL "Enable UNICODE and _UNICODE definitions" FORCE)
set(BUILD_STATIC OFF CACHE BOOL "Static build" FORCE)

#message(STATUS "CMAKE_LINKER_FLAGS_RELEASE " ${CMAKE_LINKER_FLAGS_RELEASE})
#message(STATUS "CMAKE_CXX_FLAGS_RELEASE " ${CMAKE_CXX_FLAGS_RELEASE})
#message(STATUS "CMAKE_EXE_LINKER_FLAGS_RELEASE " ${CMAKE_EXE_LINKER_FLAGS_RELEASE})
#message(STATUS "CMAKE_SHARED_LINKER_FLAGS_RELEASE " ${CMAKE_SHARED_LINKER_FLAGS_RELEASE})

#if(NOT CMAKE_CONFIGURATION_TYPES AND NOT CMAKE_BUILD_TYPE)
#    set(CMAKE_BUILD_TYPE "Debug" CACHE STRING "Choose the type of build." FORCE)
#    message(STATUS "No build type selected, defaulting to Debug")
#    set_property(CACHE CMAKE_BUILD_TYPE PROPERTY STRINGS "Debug" "Release" "MinSizeRel" "RelWithDebInfo")
#endif()

function(apply_common_settings target)
    # Debug-specific definitions
    target_compile_definitions(${target} PRIVATE 
        $<$<CONFIG:Debug>:_REALLY_WANT_DEBUG>
    )

    # Static build definitions
    if(BUILD_STATIC)
        target_compile_definitions(${target} PRIVATE 
            __STATIC_LINK
            GPackage="$<TARGET_PROPERTY:${target},NAME>"
        )
    endif()

    # Compatibility definitions
    if(UNDYING_COMPAT)
        target_compile_definitions(${target} PRIVATE 
            UNDYING_COMPAT
        )
    endif()

    # Platform-specific settings
    if(CMAKE_COMPILER_IS_GNUCXX)
        target_compile_definitions(${target} PRIVATE 
            __LINUX_X86__
        )
        target_compile_options(${target} PRIVATE 
            -m32 
            -fpermissive 
            -fno-strict-aliasing
        )
        target_link_options(${target} PRIVATE
            -m32
        )
    endif()

    if(MSVC)
        target_compile_definitions(${target} PRIVATE
            _CRT_NO_VA_START_VALIDATION
            _CRT_SECURE_NO_WARNINGS
            _CRT_SECURE_NO_DEPRECATE
            _CRT_NONSTDC_NO_DEPRECATE
            _CRT_NON_CONFORMING_SWPRINTFS
            WINDOWS_IGNORE_PACKING_MISMATCH
        )
        target_compile_options(${target} PRIVATE 
            # todo: /EHsc for game, and /EHa for editor?
            #/EHsc
            /EHa # switch to /EHa from /EHsc for SEH. this impacts performance
            /Zc:wchar_t-
            /W3
            /WX
            /MP  # Use all cores
            /FS  # Enable parallel PDB writing
        )

        if (NOT BUILD_X64)
            target_compile_options(${target} PRIVATE 
                /Zp4
            )
        endif()
        
        # Set to use MultiThreaded DLL runtime
        #set_property(TARGET ${target} PROPERTY
        #    MSVC_RUNTIME_LIBRARY "MultiThreadedDLL$<$<CONFIG:Debug>:Debug>")
        
        target_link_options(${target} PRIVATE
            /SAFESEH:NO
            #$<$<CONFIG:Debug>:/DEBUG:FASTLINK>
            #$<$<CONFIG:Debug>:/INCREMENTAL:NO>
        )

        # only if not UNDYING_COMPAT or always?
        if (NOT UNDYING_COMPAT)
            # Compiler flags for whole program optimization (/GL) in Release mode
            target_compile_options(${target} PRIVATE 
                $<$<CONFIG:Release>:/GL>
            )

            # Linker flags for link-time optimizations (/LTCG) in Release mode
            target_link_options(${target} PRIVATE 
                $<$<CONFIG:Release>:/LTCG>
            )
        endif()

        # Debug symbols
        #target_compile_options(${target} PRIVATE 
        #    $<$<CONFIG:Release>:/Zi>
        #)
        #target_link_options(${target} PRIVATE 
        #    $<$<CONFIG:Release>:/DEBUG>
        #)

        #set(CMAKE_PDB_OUTPUT_DIRECTORY "path/to/output")
    endif()

    # Common definitions for all platforms
    if(ENABLE_UNICODE)
        target_compile_definitions(${target} PRIVATE
            UNICODE
            _UNICODE
        )
    endif()

    if(BUILD_X64)
        target_compile_definitions(${target} PRIVATE
            BUILD_64
        )
    endif()

    #target_link_options(${target} PRIVATE
	#	/NODEFAULTLIB:msvcp140.lib
	#	/NODEFAULTLIB:msvcp140d.lib
	#	/NODEFAULTLIB:vcruntime140.lib
	#	/NODEFAULTLIB:vcruntime140d.lib
	#	/NODEFAULTLIB:ucrtbase.lib
	#	/NODEFAULTLIB:libc.lib
	#)
endfunction()

function(set_fixed_base_address target base_address)
    if(MSVC AND NOT BUILD_X64)
        target_link_options(${target} PRIVATE
	        /DYNAMICBASE:NO
	        /BASE:${base_address}
        )
    endif()
endfunction()

# caused crashes
#set(CMAKE_INTERPROCEDURAL_OPTIMIZATION TRUE) #link time optimization

# Disable C++ RTTI, not needed
#string(REPLACE "/GR" "" CMAKE_CXX_FLAGS ${CMAKE_CXX_FLAGS})
#set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /GR-")

# Clear any default exception handling flags, if set by the toolchain or elsewhere
string(REPLACE "/EHsc" "" CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS}")
string(REPLACE "/EHs" "" CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS}")
string(REPLACE "/EHa" "" CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS}")

if(MSVC)
# Ensure `RelWithDebInfo` has the same settings as `Release`
# This keeps /Ob2 instead of /Ob1 and /INCREMENTAL:NO instead of /INCREMENTAL
set(CMAKE_CXX_FLAGS_RELWITHDEBINFO "${CMAKE_CXX_FLAGS_RELEASE} /Zi")
set(CMAKE_C_FLAGS_RELWITHDEBINFO "${CMAKE_C_FLAGS_RELEASE} /Zi")

set(CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO "${CMAKE_EXE_LINKER_FLAGS_RELEASE} /debug")
set(CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO "${CMAKE_SHARED_LINKER_FLAGS_RELEASE} /debug")
else()
#todo: gcc/clang
endif()

add_compile_options(/Zc:__cplusplus)

# Set global runtime output directory
#set(CMAKE_RUNTIME_OUTPUT_DIRECTORY "C:/Clive Barkers Undying NEWENGIE/System")
#set(OUTPUT_DIRECTORY "C:/Clive Barkers Undying NEWENGIE/System")
#set(CMAKE_RUNTIME_OUTPUT_DIRECTORY_DEBUG "${OUTPUT_DIRECTORY}")
#set(CMAKE_RUNTIME_OUTPUT_DIRECTORY_RELEASE "${OUTPUT_DIRECTORY}")

# Set primary output to source System folder
set(LOCAL_SYSTEM_DIR "${CMAKE_SOURCE_DIR}/System")
#set(LOCAL_SYSTEM_DIR "C:/Clive Barkers Undying NEWENGIE/System")

set(CMAKE_RUNTIME_OUTPUT_DIRECTORY "${LOCAL_SYSTEM_DIR}")
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY "${CMAKE_SOURCE_DIR}/out/Lib")


add_subdirectory(DWI)
add_subdirectory(NGF)
add_subdirectory(NGFExporter)
add_subdirectory(zlib)
#add_subdirectory(iceoryx)
add_subdirectory(Core)
add_subdirectory(RenderImGui)
add_subdirectory(Engine)
add_subdirectory(Fire)
if (MSVC)
add_subdirectory(EngineRenewal)
endif()
add_subdirectory(Editor)

add_subdirectory(Render)
add_subdirectory(IpDrv)
#add_subdirectory(ALAudio)
add_subdirectory(SoftDrv)
add_subdirectory(SwFMOD)
add_subdirectory(VulkanDrv)

# Windows only
if (MSVC)
add_subdirectory(Window)
add_subdirectory(WinDrv469)
add_subdirectory(Galaxy)
endif()

add_subdirectory(CrashHandler)

# Executables
if (MSVC)
add_subdirectory(UnrealEd)
add_subdirectory(Launch)
add_subdirectory(Setup)
endif()
add_subdirectory(UCC)

# Testing
#add_subdirectory(Commandlets)
#add_subdirectory(udemo)
#add_subdirectory(ALAudio)

# Set game directory for copies
set(GAME_OUTPUT_DIRECTORY "C:/Clive Barkers Undying NEWENGIE/System")

# Add the copy target at the very end
#add_custom_target(${PROJECT_NAME} ALL
#    COMMAND ${CMAKE_COMMAND} -E make_directory "${GAME_OUTPUT_DIRECTORY}"
#    COMMAND ${CMAKE_COMMAND} -E copy_directory_if_different ${LOCAL_SYSTEM_DIR} ${GAME_OUTPUT_DIRECTORY}
#    COMMENT "Copying changed binaries to game directory: ${GAME_OUTPUT_DIRECTORY}"
#)





