/*=============================================================================
	UPkgCommandlet.cpp: Imports/Exports data to/from packages.
	Copyright 1997-1999 Epic Games, Inc. All Rights Reserved.

Revision history:
	* Created by <PERSON>.
=============================================================================*/

#include "Editor.h"

/*-----------------------------------------------------------------------------
	UExecCommandlet.
-----------------------------------------------------------------------------*/

class UExecCommandlet : public UCommandlet
{
	DECLARE_CLASS(UExecCommandlet,UCommandlet,CLASS_Transient,Commandlets);
	void StaticConstructor()
	{
		guard(StaticConstructor::StaticConstructor);

		LogToStdout     = 0;
		IsClient        = 1;
		IsEditor        = 1;
		IsServer        = 1;
		LazyLoad        = 1;
		ShowErrorCount  = 1;

		unguard;
	}
	INT Main( const TCHAR* Parms )
	{
		guard(UExecCommandlet::Main);

		// Create the editor class.
		UClass* EditorEngineClass = UObject::StaticLoadClass( UEditorEngine::StaticClass(), NULL, TEXT("ini:Engine.Engine.EditorEngine"), NULL, LOAD_NoFail | LOAD_DisallowFiles, NULL );
		GEditor  = ConstructObject<UEditorEngine>( EditorEngineClass );
		GEditor->UseSound = 0;
		GEditor->Init();
		GIsRequestingExit = 1; // Causes ctrl-c to immediately exit.

		FString Command, PkgPath, PkgName, PkgDir;

		// Make sure we got all params.
		if( !ParseToken(Parms,Command,0) )
			appErrorf(TEXT("Command not specified."));

		if( !ParseToken(Parms,PkgPath,0) )
			appErrorf(TEXT("Package path not specified"));

		bool bCompress = appStrfind( Parms, TEXT("-COMPRESS") ) != 0;

		// Parse the package name 
		if( PkgPath.InStr( TEXT("\\") ) != -1 )
		{
			PkgName = PkgPath.Right( PkgPath.Len() - PkgPath.InStr( TEXT("\\"), 1 ) - 1 );
			PkgDir = PkgPath.Left( PkgPath.InStr( TEXT("\\"), 1 ) );
			if( !PkgDir.Len() )
				PkgDir = TEXT(".");
		}
		else
		{
			PkgName = PkgPath;
			PkgDir = TEXT(".");
		}

		UObject* Package = LoadPackage(NULL,*PkgPath,LOAD_NoFail);

		if( !Package )
			appErrorf(TEXT("Package not found"));

		GLog->SuppressAllEnable(0);

		GEditor->ParentContext = Package;

		FOutputDevice* TempLog = GLog;
		GLog = GWarn;
		
		GWarn->Logf(TEXT("Executing %s"), *Command);
		GEditor->Exec( *Command ),

		GLog = TempLog;

		// Saving package.  Don't warn when saving a smaller file than already on disk.
		GWarn->Logf( TEXT("Saving : %s"), Package->GetName() );
		// Process all textures.
		//UObject::ResetLoaders(NULL,0,1);

		SavePackage( Package, NULL, RF_Standalone, *(FString(Package->GetName())+TEXT(".u")), GWarn, NULL, bCompress );
		//GEditor->Exec( *FString::Printf( TEXT("OBJ SAVEPACKAGE PACKAGE=\"%s\" FILE=\"%s\" WARN=0"), *PkgName, *PkgPath ) );

		GIsRequestingExit=1;
		return 0;

		unguard;
	}
};
IMPLEMENT_CLASS(UExecCommandlet)

IMPLEMENT_PACKAGE(Commandlets)

/*-----------------------------------------------------------------------------
	The End.
-----------------------------------------------------------------------------*/